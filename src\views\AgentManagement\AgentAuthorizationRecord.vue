<template>
  <div>
    <el-drawer
      custom-class="agent-auth-record-drawer"
      :visible.sync="authRecordVisible"
      size="80%"
      :append-to-body="true"
      :modal-append-to-body="false"
      :close-on-click-modal="true"
    >
      <div class="draw-title" slot="title">{{ deviceTypeName }} - {{ $t('agent.authorizationRecord') }}</div>
      <div class="agent-auth-details">
        <!-- 筛选区域 -->
        <div class="auth-header">
          <div class="filter-row">
            <div class="filter-item">
              <span class="filter-label">{{ $t('agent.startDate') }}</span>
              <el-date-picker
                v-model="filterParams.startDate"
                type="date"
                :placeholder="$t('agent.startDate')"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                @change="handleFilterChange"
              ></el-date-picker>
            </div>
            <div class="filter-item">
              <span class="filter-label">{{ $t('agent.endDate') }}</span>
              <el-date-picker
                v-model="filterParams.endDate"
                type="date"
                :placeholder="$t('agent.endDate')"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                @change="handleFilterChange"
              ></el-date-picker>
            </div>
            <div class="filter-item">
              <span class="filter-label">{{ $t('agent.deviceModel') }}</span>
              <el-select v-model="filterParams.deviceModel" :placeholder="$t('agent.allOptions')" @change="handleFilterChange">
                <el-option :label="$t('agent.allOptions')" value=""></el-option>
                <el-option label="A2D HD 4K" value="A2D HD 4K"></el-option>
              </el-select>
            </div>
            <div class="filter-item">
              <span class="filter-label">{{ $t('agent.eventType') }}</span>
              <el-select v-model="filterParams.eventType" :placeholder="$t('agent.allOptions')" @change="handleFilterChange">
                <el-option :label="$t('agent.allOptions')" value=""></el-option>
                <el-option :label="$t('agent.authorization')" value="0"></el-option>
                <el-option :label="$t('agent.revokeAuthorization')" value="1"></el-option>
              </el-select>
            </div>
            <div class="filter-item">
              <el-button type="primary" @click="exportRecords">{{ $t('agent.export') }}</el-button>
            </div>
          </div>
        </div>

        <!-- 内容 -->
        <div class="auth-content" v-loading="tableLoading">
          <!-- 表格 -->
          <new-table
            class="auth-table"
            :data="tableData.data"
            :loading="tableLoading"
            :header-data="headerData"
            :hasIndex="true"
          >
            <template #operator="scope">
              <span>{{ scope.row.operator || $t('agent.system') }}</span>
            </template>
            <template #addedCount="scope">
              <span>{{ scope.row.addedCount || 20 }}</span>
            </template>
          </new-table>
        </div>
      </div>

      <!-- 分页 -->
      <div class="auth-pagination">
        <Pagination
          :total-pages="totalPages"
          :total="totalUsers"
          :page-size="pageSize"
          :pageSizes="[10, 20, 50, 100]"
          :page-no="pageNo"
          @changePageSize="changePageSize"
          @changePageNo="changePageNo"
        />
      </div>
    </el-drawer>
  </div>
</template>

<script>
import newTable from '@/components/func-components/newTable.vue';
import Pagination from '@/components/func-components/Pagination';
import { getAuthRecordListPage } from '@/api/agent';

export default {
  name: "AgentAuthorizationRecord",
  components: {
    newTable,
    Pagination
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    agentInfo: {
      type: Object,
      default: () => ({})
    },
    deviceType: {
      type: String,
      default: 'a2d_hd_4k'
    },
    authRecords: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableLoading: false,
      // 分页相关
      totalUsers: 0,
      totalPages: 1,
      pageSize: 20,
      pageNo: 1,
      // 筛选参数
      filterParams: {
        startDate: '',
        endDate: '',
        deviceModel: '',
        eventType: ''
      },
      // 表格数据
      tableData: {
        data: []
      }
    };
  },
  computed: {
    authRecordVisible: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit("update:show", val);
      },
    },
    deviceTypeName() {
      const typeMap = {
        'a2d_hd_4k': 'A2D HD 4K',
        'design_service': this.$t('agent.designService'),
        'ai_software': this.$t('agent.aiSoftware')
      };
      return typeMap[this.deviceType] || this.deviceType;
    },
    headerData() {
      return [
        {
          prop: "operationTime",
          minWidth: "15%",
          noTip: false,
          getLabel: () => this.$t('agent.operationTime'),
        },
        {
          prop: "eventType",
          minWidth: "12%",
          noTip: false,
          getLabel: () => this.$t('agent.eventType'),
        },
        {
          prop: "deviceModel",
          minWidth: "12%",
          noTip: false,
          getLabel: () => this.$t('agent.deviceModel'),
        },
        {
          prop: "customerName",
          minWidth: "18%",
          noTip: false,
          getLabel: () => this.$t('agent.endCustomer'),
        },
        {
          prop: "deviceSN",
          minWidth: "20%",
          noTip: false,
          getLabel: () => this.$t('agent.deviceSN'),
        },
        {
          prop: "addedCount",
          minWidth: "13%",
          noTip: false,
          getLabel: () => this.$t('agent.newAddedCount'),
        },
        {
          prop: "operator",
          minWidth: "10%",
          noTip: false,
          getLabel: () => this.$t('agent.operator'),
        }
      ];
    }
  },
  watch: {
    show(newValue) {
      if (newValue) {
        console.log('授权记录抽屉打开，代理商信息:', this.agentInfo);
        this.initData();
        this.loadAuthRecords();
      }
    },
    // 监听设备类型变化
    deviceType(newValue) {
      console.log('设备类型变化:', newValue);
      if (this.show) {
        this.pageNo = 1;
        this.loadAuthRecords();
      }
    }
  },
  methods: {
    // 初始化数据
    initData() {
      this.pageNo = 1;
      this.filterParams = {
        startDate: '',
        endDate: '',
        deviceModel: '',
        eventType: ''
      };
    },

    // 加载授权记录数据
    async loadAuthRecords() {
      if (!this.agentInfo || !this.agentInfo.orgCode) {
        console.warn('缺少代理商信息，无法加载授权记录');
        return;
      }

      this.tableLoading = true;
      console.log('开始加载授权记录分页数据...', {
        orgCode: this.agentInfo.orgCode,
        deviceType: this.deviceType,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        filterParams: this.filterParams
      });

      try {
        // 构建请求参数
        const requestData = {
          pageNum: this.pageNo,
          pageSize: this.pageSize,
          orgCode: this.agentInfo.orgCode
        };

        // 添加可选的筛选参数
        if (this.filterParams.deviceModel) {
          requestData.deviceType = this.filterParams.deviceModel;
        } else {
          // 如果没有选择具体设备型号，使用当前选项卡的设备类型
          requestData.deviceType = this.getDeviceTypeForAPI(this.deviceType);
        }

        if (this.filterParams.eventType) {
          requestData.eventType = this.filterParams.eventType;
        }

        if (this.filterParams.startDate) {
          requestData.startTime = new Date(this.filterParams.startDate).getTime();
        }

        if (this.filterParams.endDate) {
          requestData.endTime = new Date(this.filterParams.endDate).getTime();
        }

        const response = await getAuthRecordListPage(requestData);

        if (response && response.code === 0 && response.data) {
          const pageData = response.data;

          // 格式化数据
          const formattedData = this.formatAuthRecords(pageData.records || []);

          this.tableData.data = formattedData;
          this.totalUsers = pageData.total || 0;
          this.totalPages = pageData.pages || 1;

          console.log('授权记录加载成功:', {
            total: this.totalUsers,
            pages: this.totalPages,
            records: formattedData.length
          });
        } else {
          console.warn('获取授权记录失败:', response?.message);
          this.tableData.data = [];
          this.totalUsers = 0;
          this.totalPages = 1;
        }
      } catch (error) {
        console.error('加载授权记录异常:', error);
        this.tableData.data = [];
        this.totalUsers = 0;
        this.totalPages = 1;

        if (this.$MessageAlert) {
          this.$MessageAlert({
            text: this.$t('agent.loadDataError') || '加载数据失败',
            type: 'error'
          });
        }
      } finally {
        this.tableLoading = false;
      }
    },

    // 格式化授权记录数据
    formatAuthRecords(records) {
      return records.map(record => ({
        operationTime: record.date || '',
        eventType: this.getEventTypeText(record.operationType),
        deviceModel: record.type || this.getDeviceModelText(this.deviceType),
        customerName: record.agentOrg || '',
        deviceSN: record.deviceSn || '',
        addedCount: record.count || 0,
        operator: record.operator || this.$t('agent.system')
      }));
    },

    // 获取事件类型文本
    getEventTypeText(operationType) {
      const eventTypeMap = {
        0: this.$t('agent.authorization') || '授权',
        1: this.$t('agent.revokeAuthorization') || '撤销授权'
      };
      return eventTypeMap[operationType] || this.$t('agent.unknown') || '未知';
    },

    // 获取设备型号文本
    getDeviceModelText(deviceType) {
      const deviceModelMap = {
        'a2d_hd_4k': 'A2D HD 4K',
        'design_service': this.$t('agent.designService') || '设计服务',
        'ai_software': this.$t('agent.aiSoftware') || 'AI软件'
      };
      return deviceModelMap[deviceType] || deviceType;
    },

    // 获取API需要的设备类型参数
    getDeviceTypeForAPI(deviceType) {
      const deviceTypeMap = {
        'a2d_hd_4k': 'A2D HD 4K',
        'design_service': 'design_service',
        'ai_software': 'ai_software'
      };
      return deviceTypeMap[deviceType] || deviceType;
    },

    // 分页相关方法
    changePageSize(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.loadAuthRecords();
    },

    changePageNo(val) {
      this.pageNo = val;
      this.loadAuthRecords();
    },

    // 筛选条件变化
    handleFilterChange() {
      console.log('筛选条件变化:', this.filterParams);
      this.pageNo = 1;
      this.loadAuthRecords();
    },

    // 时间格式化辅助方法
    formatDateForAPI(dateString) {
      if (!dateString) return null;
      try {
        return new Date(dateString).getTime();
      } catch (error) {
        console.warn('时间格式化失败:', dateString, error);
        return null;
      }
    },

    // 导出记录
    exportRecords() {
      if (!this.agentInfo || !this.agentInfo.orgCode) {
        this.$message.warning(this.$t('agent.missingOrgCode') || '缺少代理商信息');
        return;
      }

      console.log('导出授权记录', {
        orgCode: this.agentInfo.orgCode,
        deviceType: this.deviceType,
        filterParams: this.filterParams
      });

      // TODO: 实现真实的导出功能
      // 可以调用导出接口，传递当前的筛选参数
      this.$message.success(this.$t('agent.exportInProgress'));
    },

    // 关闭抽屉
    closeDrawer() {
      this.$emit("update:show", false);
    }
  }
};
</script>

<style lang="scss" scoped>
.agent-auth-details {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);

  .auth-header {
    padding: 24px;
    border-bottom: 1px solid $hg-border-color;
    background: $hg-background-color;

    .filter-row {
      display: flex;
      align-items: center;
      gap: 24px;
      flex-wrap: wrap;

      .filter-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .filter-label {
          color: $hg-primary-fontcolor;
          font-size: 14px;
          white-space: nowrap;
          min-width: 60px;
        }

        ::v-deep .el-date-editor {
          width: 160px;

          .el-input__inner {
            background: $hg-main-black;
            border-color: $hg-border-color;
            color: $hg-primary-fontcolor;

            &::placeholder {
              color: $hg-secondary-fontcolor;
            }
          }
        }

        ::v-deep .el-select {
          width: 120px;

          .el-input__inner {
            background: $hg-main-black;
            border-color: $hg-border-color;
            color: $hg-primary-fontcolor;
          }
        }

        .el-button {
          min-width: 80px;
        }
      }
    }
  }

  .auth-content {
    flex: 1;
    padding: 24px;
    overflow: hidden;

    .auth-table {
      height: calc(100vh - 300px);

      ::v-deep .el-table {
        background: #27292E;

        .el-table__header-wrapper {
          .el-table__header {
            th {
              background: #27292E;
              color: #e4e8f7;
              border-bottom: 1px solid #38393d;
              font-weight: normal;
            }
          }
        }
l
        .el-table__body-wrapper {
          .el-table__body {
            tr {
              background: #27292E;

              &:hover {
                background: #2f3136 !important;
              }

              td {
                border-bottom: 1px solid #38393d;
                color: #e4e8f7;
              }
            }
          }
        }
      }
    }
  }
}

.auth-pagination {
  padding: 16px 24px;
  border-top: 1px solid $hg-border-color;
  background: $hg-background-color;
  display: flex;
  justify-content: center;
}
</style>

<style lang="scss">
.agent-auth-record-drawer {
  .el-drawer__header {
    background: $hg-main-black;
    color: $hg-primary-fontcolor;
    border-bottom: 1px solid $hg-border-color;
    padding: 16px 24px;
    margin-bottom: 0;

    .draw-title {
      color: $hg-primary-fontcolor;
      font-size: 16px;
      font-weight: bold;
    }
  }

  .el-drawer__body {
    background: $hg-background-color;
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  // 下拉框样式
  .el-select-dropdown {
    background: $hg-main-black;
    border: 1px solid $hg-border-color;

    .el-select-dropdown__item {
      color: $hg-primary-fontcolor;

      &:hover {
        background: $hg-hover-bg-color;
      }

      &.selected {
        color: $hg-main-blue;
        background: $hg-hover-bg-color;
      }
    }
  }

  // 日期选择器样式
  .el-picker-panel {
    background: $hg-main-black;
    border: 1px solid $hg-border-color;
    color: $hg-primary-fontcolor;

    .el-picker-panel__body {
      .el-date-picker__header {
        color: $hg-primary-fontcolor;
      }

      .el-picker-panel__content {
        .el-date-table {
          td {
            color: $hg-primary-fontcolor;

            &.available:hover {
              color: $hg-main-blue;
            }

            &.current:not(.disabled) {
              color: $hg-main-blue;
            }
          }
        }
      }
    }
  }


}
</style>