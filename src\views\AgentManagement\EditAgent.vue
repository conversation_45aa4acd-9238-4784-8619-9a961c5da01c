<template>
  <div v-if="show" class="edit-custom-box">
    <div class="header">
      <p class="back-btn border finger" @click="goBack"><i class="iconfont icon-arrow-back" />{{$t('common.goback')}}</p>
      <p class="header-title">{{$t('agent.editAgentTitle')}}</p>
    </div>
    <div class="content-box">
      <div class="content-left">
        <div class="title">
          <p class="title-text">{{$t('agent.basicInfo')}}</p>
          <el-button v-if="editAgentObj.status != -1 && editAgentObj.status != 0 && saveBtn" class="title-btn" :loading="editLoading" type="primary" @click="saveCustomInfoFunc('editAgentRuleForm', 'save')">{{$t('common.save')}}</el-button>
        </div>
        <div class="edit-custom-form custom-form">
          <el-form ref="editAgentRuleForm" class="left-form" :model="editAgentObj" :label-width="'100px'" :rules="rules">           
            <!-- 代理商名称 -->
            <el-form-item class="edit-agent-label">
              <template slot="label">
              <span style="color: #C74040;">*</span>{{$t('agent.agentName')}}
              </template>
              <el-input v-model="editAgentObj.agentName" :disabled="true" />
            </el-form-item>
            
            <!-- 客户编码 -->
            <el-form-item class="edit-agent-label">
              <template slot="label">
              <span style="color: #C74040;">*</span>{{$t('agent.agentCode')}}
              </template>
              <el-input v-model="editAgentObj.agentCode" type="text" :disabled="true" />
            </el-form-item>

             <!-- 助记码 -->
            <el-form-item :label="$t('agent.memorySn')" class="edit-agent-label">
              <el-input v-model="editAgentObj.customerCode" type="text" />
            </el-form-item>

            <!-- 时区 -->
            <el-form-item prop="tzCode" class="edit-agent-label">
              <template slot="label">
              <span style="color: #C74040;">*</span>{{$t('agent.timezone')}}
              </template>
              <div class="input-box"><Select :select-options="timezoneList" :value="editAgentObj.tzCode" @change="changeTimezone" /></div>
            </el-form-item>

            <!-- 结算币种 -->
            <el-form-item :label="$t('agent.currency')" class="edit-agent-label">
              <div class="input-box"><Select :select-options="currencyList" :value="editAgentObj.currency" @change="changeCurrency" /></div>
            </el-form-item>

            <!-- 邮箱 -->
            <el-form-item :label="$t('agent.email')" class="edit-agent-label">
              <el-input v-model="editAgentObj.email" type="text" :disabled="true" />
            </el-form-item>

            <!-- 地址 -->
            <el-form-item :label="$t('agent.address')" class="edit-agent-label">
              <el-input v-model="editAgentObj.address" type="text"  />
            </el-form-item>
            <!-- 负责人 -->
            <el-form-item :label="$t('agent.leader')" class="edit-agent-label">
              <el-input v-model="editAgentObj.leader" type="text" />
            </el-form-item>
            
            <!-- 手机号码 -->
            <el-form-item :label="$t('agent.mobile')" class="edit-agent-label">
              <div class="area-code"><Select :select-options="countryListArrayComputed" :value="editAgentObj.mobilePrefix" :placeholder="$t('agent.areaCode')" @change="changeAreaCode" /></div>
              <el-input v-model="editAgentObj.mobile" type="text" :placeholder="$t('agent.mobilePlaceholder')" />
            </el-form-item>
            
            <!-- 业务人员 -->
            <el-form-item prop="businessUserCodes" :label="$t('agent.businessUser')" class="edit-agent-label">
              <div class="input-box" style="width: 96%;">
                <Select 
                  :placeholder="$t('agent.businessUserPlaceholder')" 
                  :select-options="businessUserList" 
                  :value="editAgentObj.businessUserCodes && editAgentObj.businessUserCodes.length > 0 ? editAgentObj.businessUserCodes[0] : ''" 
                  :is-multiple="false" 
                  @change="changeBusinessUser" 
                />
              </div>
            </el-form-item>
            <!-- 技术支持 -->
            <el-form-item :label="$t('agent.techSupport')" prop="supportUserCodes" class="edit-agent-label">
              <div class="input-box">
                <Select
                  :placeholder="$t('agent.techSupportPlaceholder')"
                  :select-options="techSupportList"
                  :value="editAgentObj.supportUserCodes || []"
                  :isMultiple="true"
                  @change="changeTechSupport"
                />
              </div>
            </el-form-item>
          </el-form>
          <el-form ref="editRightRuleForm" class="right-form" label-position="left" :label-width="'100px'" :model="editAgentObj" :rules="rules">
            <!-- 收款信息 -->
            <el-form-item :class="$i18n.locale == 'zh' ? '' : 'edit-business-label'" class="edit-agent-label">
              <template slot="label">
                <span style="color: #C74040;">*</span> {{ $t('customer.pay') }}
              </template>
              <div class="input-box" :class="isShowError ? 'bank-info' : ''" style="position: relative;width: 96%;">
                <Select :select-options="bankInfoList" :placeholder="$t('customer.bankError')" :value="editAgentObj.accountCode" @change="changeBankInfo" />
                <span v-if="isShowError" class="error-tips">{{ $t('customer.bankError') }}</span>
              </div>
            </el-form-item>
            <div v-if="nowSelectBank" class="bank-info-box">
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.bankName') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectBank.bankName }}</li>
              </ul>
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.bankLocation') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectBank.bankLocal }}</li>
              </ul>
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.bankAdress') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectBank.bankAddress }}</li>
              </ul>
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.bankCode') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectBank.bankSn }}</li>
              </ul>
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.bankAccouont') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectBank.accountName }}</li>
              </ul>
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.bankNumber') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectBank.accountSn }}</li>
              </ul>
            </div>

            <!-- 账单抬头 -->
            <el-form-item class="edit-agent-label">
              <template slot="label">
                <span style="color: #C74040;">*</span> {{ $t('customer.billFrom') }}
              </template>
              <div class="input-box" :class="isShowHeaderError ? 'bank-info' : ''" style="position: relative;width: 96%;">
                <Select :select-options="headerInfoList" :placeholder="$t('customer.bankError')" :value="editAgentObj.headerCode" @change="changeHeaderInfo" />
                <span v-if="isShowHeaderError" class="error-tips">{{ $t('customer.bankError') }}</span>
              </div>
            </el-form-item>
            <div v-if="nowSelectHeader" class="bank-info-box">
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.tel') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectHeader.tel }}</li>
              </ul>
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.mail') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectHeader.mail }}</li>
              </ul>
              <ul style="display: flex;">
                <li class="bank-title" :class="$i18n.locale == 'zh' ? '' : 'en-title'">{{ $t('customer.address') }}：</li>
                <li class="bank-content" :class="$i18n.locale == 'zh' ? '' : 'en-content'">{{ nowSelectHeader.address }}</li>
              </ul>
            </div>
            
            <!-- 代理范围 -->
            <el-form-item :label="$t('agent.agentScope')" class="edit-agent-label agent-scope-form-item agent-scope-vertical">
              <div class="agent-scope-box">
                <div class="agent-scope-info-box">
                  <div class="scope-checkboxes-vertical">
                    <el-checkbox v-model="editAgentObj.deviceAgent" disabled>{{$t('agent.device')}}</el-checkbox>
                    <el-checkbox v-model="editAgentObj.designAgent" disabled>{{$t('agent.designService')}}</el-checkbox>
                    <el-checkbox v-model="editAgentObj.aiAgent" disabled>{{$t('agent.aiSoftware')}}</el-checkbox>
                  </div>
                </div>
              </div>
            </el-form-item>
            
          </el-form>
        </div>
      </div>
      <div class="content-right">
        <div class="title">
          <p class="title-text">{{$t('agent.customerAccount')}}</p>
          <div v-if="editAgentObj.status == 0" v-permission="['editAgent', 'delete']" class="pass-btn">
            <VueButton
              class="unpass-btn"
              width="104"
              sizes="big"
              @click.native="examineAgentFunc(0)"
            >
              {{$t('agent.unpass')}}
            </VueButton>
            <VueButton
              width="104"
              type="primary"
              sizes="big"
              @click.native="beforeExamine"
            >
              {{$t('agent.pass')}}
            </VueButton>
          </div>
        </div>
        <div class="custom-form">
          <el-form :model="editAgentObj" @submit.native.prevent>
            <el-form-item :label="$t('agent.account')" class="edit-agent-label">
              <el-input v-model="editAgentObj.email" type="text" :disabled="true" />
            </el-form-item>
            <el-form-item :label="$t('agent.password')" class="edit-agent-label">
              <el-input v-model="password" type="password" :disabled="true" />
              <VueButton
                class="edit-password-btn"
                width="104"
                sizes="big"
                icon="icon_refresh_nor iconfont-24"
                @click.native="resetPasswordFunc"
              >
                {{$t('common.reset')}}
              </VueButton>
            </el-form-item>
            <el-form-item :label="$t('agent.status')" class="edit-agent-label">
              <div v-if="editAgentObj.status === 1 || editAgentObj.status === 2">
                <!-- <el-radio v-model="editAgentObj.status" :disabled="permission.includes('editAgent')===false" :label="1" @change="changeStatusFunc">启用</el-radio>
                <el-radio v-model="editAgentObj.status" :disabled="permission.includes('editAgent')===false" :label="2" @change="changeStatusFunc">禁用</el-radio> -->
                <el-radio v-model="editAgentObj.status"  :label="1" @change="changeStatusFunc">{{$t('agent.enable')}}</el-radio>
                <el-radio v-model="editAgentObj.status"  :label="2" @change="changeStatusFunc">{{$t('agent.disable')}}</el-radio>
              </div>
              <p v-if="editAgentObj.status === 0" class="wait-examine">{{$t('agent.approvalPending')}}</p>
              <p v-if="editAgentObj.status === -1" class="unpass">{{$t('agent.statusUnpass')}}</p>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import { mapState } from 'vuex'
import Select from '@/components/func-components/Select'
import { refreshLabel } from '@/assets/script/refreshLabel.js'
import { resetPassword } from '@/api/organization'
import { examineCustomer, getAllCurrency, getBankList, refreshBankAccount, getHeaderList } from '@/api/customer'
import { clipboardFunc, clipDestroy } from '@/assets/script/clipBoardFunc.js'
import { COMMON_CONSTANTS } from '@/assets/script/constants.js'
import { getTimezoneList, getBusinessUserList, getAreaList } from '@/api/common'
import { changeAgentStatus, getAgentInfo } from '@/api/agent'


export default {
  name: 'EditAgent',
  components: {
    Select
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    editAgentData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    areaCodeArr: {
      type: Array,
      default: () => {
        return []
      }
    },
    saveBtn: {
      type: Boolean,
      default: true
    },
  },
  data() {
    var checkAgentCode = (rule, value, callback) => {
      if (value !== '' && !COMMON_CONSTANTS.CUSTOMER_CODE_RULE.test(value)) {
        return callback(new Error(this.$t('customer.orgSnErro')))
      } else {
        callback()
      }
    }
    var checkMobile = (rule, value, callback) => {
      if (value) {
        if (!COMMON_CONSTANTS.PHONE_RULE.test(value)) {
          return callback(new Error(this.$t('agent.mobileFormatError')))
        }
      }
      callback()
    }
    
    return {
      showCRM: false,
      nowSelectBank: null,
      nowSelectHeader: null,
      originalData: null, // 原始数据快照
      editAgentObj: {
        agentCode: '',
        agentName: '',
        customerCode: '',
        tzCode: '',
        address: '',
        leader: '',
        email: '',
        mobile: '',
        mobilePrefix: '+86',
        currency: '',
        maxDiscount: 0,
        businessUserCodes: [],
        supportUserCodes: [],
        accountCode: '',
        headerCode: '',
        deviceAgent: false,
        designAgent: false,
        aiAgent: false,
        status: 0
      },
      rules: {
        agentCode: [
          { required: true, message: this.$t('agent.agentCodeRequired') },
          { validator: checkAgentCode, trigger: 'blur' }
        ],
        agentName: [
          { required: true, message: this.$t('agent.agentNameRequired') }
        ],
        mobile: [
          { validator: checkMobile, trigger: 'blur' }
        ],
        currency: [
          { required: true, message: this.$t('agent.currencyRequired') }
        ],
        businessUserCodes: [
          { required: true, message: this.$t('agent.businessUserRequired'), trigger: 'change' }
        ],
        // supportUserCodes: [
        //   { required: true, message: this.$t('agent.techSupportRequired'), trigger: 'change', type: 'array', min: 1 }
        // ],
      },
      bankInfoList: [],
      headerInfoList: [],
      password: '123456',
      timezoneList: [],
      areaList: [],
      bucketList: [],
      businessUserList: [],
      techSupportList: [],
      currencyList: [],
      editLoading: false,
      disableCode: false,
      isShowError: false,
      isShowHeaderError: false
    }
  },
  computed: {
    ...mapState({
      permission: (state) => state.permission
    }),
    countryListArrayComputed() {
      const countryListArrayNew = []
      this.areaCodeArr.forEach((item) => {
        if (this.$i18n.locale === 'zh') {
          item.label = item.countryName + ' +' + item.mobilePrefix
        } else {
          item.label = item.countryEn + ' +' + item.mobilePrefix
        }
        item.value = item.mobilePrefix
        countryListArrayNew.push(item)
      })
      return countryListArrayNew
    },
    isDisableCode() {
      return this.editAgentObj.isSynchronized || this.disableCode
    },
    // 检测是否有未保存的修改
    hasUnsavedChanges() {
      if (!this.originalData) return false
      
      // 创建当前数据的副本，排除不需要比较的字段
      const currentData = {
        agentCode: this.editAgentObj.agentCode,
        agentName: this.editAgentObj.agentName,
        customerCode: this.editAgentObj.customerCode,
        address: this.editAgentObj.address,
        leader: this.editAgentObj.leader,
        email: this.editAgentObj.email,
        mobile: this.editAgentObj.mobile,
        mobilePrefix: this.editAgentObj.mobilePrefix,
        currency: this.editAgentObj.currency,
        maxDiscount: this.editAgentObj.maxDiscount,
        businessUserCodes: this.editAgentObj.businessUserCodes,
        supportUserCodes: this.editAgentObj.supportUserCodes,
        accountCode: this.editAgentObj.accountCode,
        headerCode: this.editAgentObj.headerCode
      }
      
      return JSON.stringify(currentData) !== JSON.stringify(this.originalData)
    }
  },
  watch: {
    show(val) {
      if (val) {
        this.isShowError = false;
        this.isShowHeaderError = false;
        
        // 如果有orgCode，调用接口获取详细信息
        if (this.editAgentData && this.editAgentData.orgCode) {
          this.getAgentDetailInfo(this.editAgentData.orgCode);
        } else {
          // 否则使用传入的基础数据
          this.initAgentData();
        }
        
        Promise.allSettled([
          this.getBusinessUserListFunc(), 
          this.getTimezoneListFunc(), 
          this.getAreaList(), 
          this.getAllCurrencyFunc(), 
          this.getBankList(), 
          this.getHeaderList(),
          this.getTechSupportListFunc()
        ])
        
        setTimeout(() => {
          refreshLabel('edit-agent-label')
        }, 10)
      } else {
        this.resetForm('editAgentRuleForm')
        this.disableCode = false
      }
    }
  },
  methods: {
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    goBack() {
      // if (this.hasUnsavedChanges) {
      //   this.$Dialog({
      //     title: this.$t('common.confirmReturn'),
      //     message: this.$t('common.giveUpEditTip'),
      //     ensureBtnText: this.$t('common.confirm'),
      //     cancelBtnText: this.$t('common.cancel'),
      //     confirmAction: () => {
      //       this.$emit('update:show', false)
      //     }
      //   })
      // } else {
      //   this.$emit('update:show', false)
      // }
      this.$emit('update:show', false)
    },
    saveCustomInfoFunc(formName, type) {
    
      if (!this.editAgentObj.accountCode) {
        this.isShowError = true
        return
      }
      if(!this.editAgentObj.headerCode){
        this.isShowHeaderError = true
        return
      }

      if (this.editAgentObj.businessUserCodes && this.editAgentObj.businessUserCodes.length > 1) {
        this.$message.error(this.$t('agent.businessUserOnlyOne'));
        return
      }
      if (!this.editAgentObj.businessUserCodes || this.editAgentObj.businessUserCodes.length == 0) {
        this.$message.error(this.$t('agent.businessUserRequired'))
        return
      }
      
      console.log('Starting form validation...');
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (type === 'save') {
            this.editLoading = true
            const editData = Object.assign({}, this.editAgentObj, { isBackupCrmOrg: Number(this.disableCode) })
            this.$emit('submit', editData, type)
          } else if (type === 'pass') {
            this.examineAgentFunc(1, type)
          }
        } else {
          this.editLoading = false
          return false
        }
      })
    },

    getAllCurrencyFunc() {
      getAllCurrency().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.currencyList = res.data
            this.currencyList.forEach((item) => {
              item.label = item.currency
              item.lable_en = item.currencyEn
              item.value = item.settlementCurrency
            })
          }
        }
      })
    },

    getBankList() {
      getBankList().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.bankInfoList = res.data
            this.bankInfoList.forEach((item) => {
              item.label = item.description
              item.value = item.accountCode
            })
          }
        }
      })
    },

    getHeaderList(){
      getHeaderList().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.headerInfoList = res.data
            this.headerInfoList.forEach((item) => {
              item.label = item.title
              item.value = item.headerCode
            })
          }
        }
      })
    },

    getRefreshBankAccount(){
      this.nowSelectBank = null;
      let data = {
        'isBackupCrmOrg': Number(this.disableCode),
        'orgCode': this.editAgentObj.orgCode,
        'orgSn': this.editAgentObj.agentCode,
        'settlementCurrency': this.editAgentObj.currency
      }
      refreshBankAccount(data).then((res) => {
        if (res.code === 200) {
          this.nowSelectBank = res.data.bankInfo ? res.data.bankInfo : null;
          this.editAgentObj.accountCode = res.data.bankInfo ? res.data.bankInfo.accountCode : '';
          this.nowSelectHeader = res.data.headerInfo ? res.data.headerInfo : null;
          this.editAgentObj.headerCode = res.data.headerInfo ? res.data.headerInfo.headerCode : '';
        }
      })
    },

    getBusinessUserListFunc() {
      return new Promise((resolve) => {
        getBusinessUserList().then((res) => {
          if (res.code === 200) {
            if (res.data != null && res.data.length) {
              this.businessUserList = res.data
              this.businessUserList.forEach((item) => {
                item.label = item.realName
                item.value = item.userCode
              })
            }
          }
          resolve()
        })
      })
    },

     // 获取技术支持列表
    getTechSupportListFunc() {
      // 技术支持列表通常与业务人员列表相同，或者需要单独的API
      getBusinessUserList().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.techSupportList = res.data
            this.techSupportList.forEach((item) => {
              item.label = item.realName
              item.value = item.userCode
            })
          }
        }
      })
    },

    getTimezoneListFunc() {
      getTimezoneList().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.timezoneList = res.data
            this.timezoneList.forEach((item) => {
              item.label = this.$i18n.locale === 'zh' ? item.tzNameCn : item.tzNameEn;
              item.value = item.tzCode
            })
          }
        }
      })
    },

    async getAreaList(){
      const loop = (arr) => {
        arr.forEach((item) => {
          item.label =  this.$i18n.locale === 'zh' ? item.nameCn : item.name;
          if(item.children){
            loop(item.children)
          }
        })
        return arr
      }
      const { code, data } = await getAreaList();
      if(code == 200){
        this.areaList = loop(data);
      }
    },

    changeTimezone(value) {
      this.editAgentObj.tzCode = value
    },

    changeAreaCode(value) {
      this.editAgentObj.mobilePrefix = '+' + Number(value)
    },

    changeCurrency(value) {
      this.editAgentObj.currency = value;
      this.getRefreshBankAccount();
    },

    changeBusinessUser(value) {
      // 业务人员单选，将值转换为数组格式
      this.editAgentObj.businessUserCodes = value ? [value] : []
    },

    changeTechSupport(value) {
      this.editAgentObj.supportUserCodes = value || []
    },

    changeBankInfo(value) {
      this.isShowError = false;
      this.editAgentObj.accountCode = value;
      this.nowSelectBank = this.bankInfoList.find((item) => { return item.accountCode === value }) || null;
    },

    changeHeaderInfo(value){
      this.isShowHeaderError = false;
      this.editAgentObj.headerCode = value;
      this.nowSelectHeader = this.headerInfoList.find((item) => { return item.headerCode === value }) || null;
    },

    resetPasswordFunc() {
      const agentName = this.editAgentObj.agentName || this.$t('agent.unnamedAgent')
      
      this.$Dialog({
        title: this.$t('agent.resetPasswordConfirm'),
        message: `${this.$t('agent.resetPasswordAgent')}<${agentName}>`,
        ensureBtnText: this.$t('common.confirm'),
        cancelBtnText: this.$t('common.cancel'),
        confirmAction: () => {
          this.ensureResetPassword()
        }
      })
    },

    ensureResetPassword() {
      // 检查 userCode 是否存在
      if (!this.editAgentObj.userCode) {
        this.$MessageAlert({
          text: this.$t('agent.resetPasswordError') || '重置密码失败：缺少用户信息',
          type: 'error'
        })
        return
      }

      resetPassword({
        'userCode': this.editAgentObj.userCode
      }).then((res) => {
        if (res.code === 200) {
          if (res.data) {
            this.$Dialog({
              title: this.$t('agent.resetPasswordSuccess'),
              message: `${this.$t('agent.resetPasswordAfter')}${res.data}`,
              ensureBtnText: this.$t('common.copy'),
              cancelBtnText: this.$t('common.close'),
              updateShow: false,
              init: () => {
                clipboardFunc(res.data)
              },
              cancelAction: () => {
                clipDestroy()
              }
            })
          }
        }
      }).catch((error) => {
        console.error('重置密码接口调用失败:', error)
        this.$MessageAlert({
          text: error.message || this.$t('agent.resetPasswordError') || '重置密码失败',
          type: 'error'
        })
      })
    },

    beforeExamine() {
      this.saveAgentInfoFunc('editAgentRuleForm', 'pass')
    },

    examineAgentFunc(result, type) {
      const isPass = result ? this.$t('agent.pass') : this.$t('agent.unpass')
      const agentName = this.editAgentObj.agentName || this.$t('agent.unnamedAgent')
      
      this.$Dialog({
        title: this.$t('agent.examineTitle'),
        message: `${this.$t('agent.examineConfirm', { isPass })}\n${this.$t('agent.agentName')}<${agentName}>`,
        ensureBtnText: this.$t('common.confirm'),
        cancelBtnText: this.$t('common.cancel'),
        confirmAction: () => {
          if (result === 1) {
            const editData = Object.assign({}, this.editAgentObj, { isBackupCrmOrg: Number(this.disableCode) })
            this.$emit('submit', editData, type, result)
          } else {
            this.editAgentObj.status = -1
            this.ensureExamine(result);
          }
        }
      })
    },

    ensureExamine(result) {
      examineCustomer({
        'agentCode': this.editAgentObj.agentCode,
        'isPass': result
      }).then((res) => {
        if (res.code === 200) {
          if (res.data) {
            this.$MessageAlert({
              text: this.$t('common.operateSuccessTip'),
              type: 'success'
            })
            this.editAgentObj.status = result === 0 ? -1 : 1

            if (result !== 1) {
              this.resetForm('editAgentRuleForm')
            }
          }
        }
      })
    },

    changeStatusFunc(value) {
      const actionText = value === 1 ? this.$t('agent.enable') : this.$t('agent.disable')
      const agentName = this.editAgentObj.agentName || this.$t('agent.unnamedAgent')
      
      this.$Dialog({
        title: `${this.$t('agent.confirmChangeStatus', { actionText })}`,
        message: `${this.$t('agent.agentName')}<${agentName}>`,
        ensureBtnText: this.$t('common.confirm'),
        cancelBtnText: this.$t('common.cancel'),
        confirmAction: () => {
          this.ensureChangeStatus()
        },
        cancelAction: () => {
          this.editAgentObj.status = value === 1 ? 2 : 1
        }
      })
    },

    ensureChangeStatus() {
      changeAgentStatus({
        'orgCode': this.editAgentObj.orgCode,
        'status': this.editAgentObj.status,
        
      }).then((res) => {
        if (res.code === 200) {
          if (res.data) {
            this.$MessageAlert({
              text: `${this.editAgentObj.status === 1 ? this.$t('agent.enable') : this.$t('agent.disable')}${this.$t('common.success')}`,
              type: 'success'
            })
          }
        }
      })
    },

    // 保存原始数据快照
    saveOriginalData() {
      this.originalData = {
        agentCode: this.editAgentObj.agentCode,
        agentName: this.editAgentObj.agentName,
        customerCode: this.editAgentObj.customerCode,
        tzCode: this.editAgentObj.tzCode,
        address: this.editAgentObj.address,
        leader: this.editAgentObj.leader,
        email: this.editAgentObj.email,
        mobile: this.editAgentObj.mobile,
        mobilePrefix: this.editAgentObj.mobilePrefix,
        currency: this.editAgentObj.currency,
        maxDiscount: this.editAgentObj.maxDiscount,
        businessUserCodes: [...(this.editAgentObj.businessUserCodes || [])],
        supportUserCodes: [...(this.editAgentObj.supportUserCodes || [])],
        accountCode: this.editAgentObj.accountCode,
        headerCode: this.editAgentObj.headerCode
      }
    },

    // 获取代理商详细信息
    getAgentDetailInfo(orgCode) {
      getAgentInfo({ orgCode: Number(orgCode) }).then(res => {
        if (res.code === 200 && res.data) {
          const data = res.data;
          this.mapAgentDataToForm(data);
        }
      }).catch(error => {
        console.error('Failed to get agent info:', error);
        // 如果接口调用失败，使用传入的基础数据
        this.initAgentData();
      });
    },

    // 映射接口数据到表单
    mapAgentDataToForm(data) {
      this.editAgentObj = {
        orgCode: data.orgCode,
        userCode: data.userCode,
        agentName: data.orgName || '',
        agentCode: data.orgSn || '',
        customerCode: data.memorySn || '',
        tzCode: data.tzCode || '',
        address: data.orgAddress || '',
        leader: data.orgLeader || '',
        email: data.email || '',
        mobile: data.mobile || '',
        mobilePrefix: data.mobilePrefix || '+86',
        currency: data.settlementCurrency || '',
        status: data.status || 0,
        rangeType: data.rangeType || [],
        businessUserCodes: [],
        supportUserCodes: [],
        accountCode: '',
        headerCode: '',
        deviceAgent: false,
        designAgent: false,
        aiAgent: false
      };

      // 处理代理范围
      if (data.rangeType && data.rangeType.length > 0) {
        this.editAgentObj.deviceAgent = data.rangeType.includes(0);
        this.editAgentObj.designAgent = data.rangeType.includes(1);
        this.editAgentObj.aiAgent = data.rangeType.includes(2);
      }

      // 处理业务人员信息
      if (data.businessUserInfos && data.businessUserInfos.length > 0) {
        this.editAgentObj.businessUserCodes = data.businessUserInfos.map(user => user.userCode);
      }

      // 处理技术支持人员信息
      if (data.supportUserInfos && data.supportUserInfos.length > 0) {
        this.editAgentObj.supportUserCodes = data.supportUserInfos.map(user => user.userCode);
      }

      // 处理银行信息
      if (data.bankInfo) {
        this.editAgentObj.accountCode = data.bankInfo.accountCode || '';
        this.nowSelectBank = data.bankInfo;
      }

      // 处理账单抬头信息
      if (data.headerInfo) {
        this.editAgentObj.headerCode = data.headerInfo.headerCode || '';
        this.nowSelectHeader = data.headerInfo;
      }
      
      // 保存原始数据快照
      this.$nextTick(() => {
        this.saveOriginalData()
      })
    },

    // 初始化代理商数据（当没有orgCode时使用）
    initAgentData() {
      this.editAgentObj = JSON.parse(JSON.stringify(this.editAgentData || {}));
      
      // 设置默认值
      if (!this.editAgentObj.mobilePrefix) {
        this.editAgentObj.mobilePrefix = '+86';
      }
      if (!this.editAgentObj.status) {
        this.editAgentObj.status = 0;
      }
      
      // 处理银行和账单信息
      this.editAgentObj.accountCode = this.editAgentObj.bankInfo?.accountCode || '';
      this.editAgentObj.headerCode = this.editAgentObj.headerInfo?.headerCode || '';
      this.nowSelectBank = this.editAgentObj.bankInfo || null;
      this.nowSelectHeader = this.editAgentObj.headerInfo || null;
      
      // 处理业务人员信息
      if (this.editAgentObj.businessUserInfos && this.editAgentObj.businessUserInfos.length) {
        this.editAgentObj.businessUserCodes = this.editAgentObj.businessUserInfos.map(item => item.userCode);
      }

      // 处理业务人员信息
      if (this.editAgentObj.supportUserInfos && this.editAgentObj.supportUserInfos.length) {
        this.editAgentObj.supportUserCodes = this.editAgentObj.supportUserInfos.map(item => item.userCode);
      }
      
      // 保存原始数据快照
      this.$nextTick(() => {
        this.saveOriginalData()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-custom-box {
  z-index: 99;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0px;
  left: 0;
  background: $hg-background-color;
  padding: 14px 24px 70px 24px;
  overflow-x: auto;
  overflow-y: hidden;
  .header {
    margin-bottom: 14px;
    display: flex;
    align-items: center;
    .back-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      font-size: 12px;
      height: $hg-height-32;
      color: $hg-secondary-fontcolor;
      i {
        margin-right: 8px;
      }
    }
    .header-title {
      color: $hg-primary-fontcolor;
      margin-left: 24px;
    }
  }
  .content-box {
    display: flex;
    height: 100%;
    .content-left, .content-right {
      flex: 1;
      margin-right: 24px;
      height: 100%;
      border-radius: 4px;
      background: $hg-main-black;
      box-shadow: 0px 12px 32px 0px $hg-background-color,0px 8px 24px 0px $hg-background-color,0px 0px 16px 0px $hg-background-color;
      padding: 16px 24px;
      .title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        height: 40px;
        .title-text {
          color: $hg-primary-fontcolor;
          font-weight: bold;
          font-size: 16px;
        }
        .pass-btn {
          margin-left: auto;
          display: flex;
          .unpass-btn {
            margin-right: 24px;
          }
        }
        .title-btn {
          margin-left: auto;
          width:104px;
          background-color: #3054cc;
        }
      }
      ::v-deep .custom-form .el-form .el-form-item {
        justify-content: flex-start;
        // .el-form-item__content {
        //   width: 320px;
        // }
      }
      .edit-custom-form, .custom-form {
        display: flex;
        height: calc(100% - 56px);
        overflow: hidden;
        overflow-y: scroll;
        .input-box {
          position: relative;
          // width: 320px;
          width: 100%;
          ::v-deep .el-input-number {
            width: 100%;
            .el-input__inner {
              text-align: left;
            }
            .el-input-number__decrease, .el-input-number__increase {
              background: transparent;
              border-color: $hg-border-color;
            }
          }
          .error-tips{
            position:absolute;
            bottom: -30px;
            color:#C74040;
            font-size:12px;
          }
        }
        .area-code {
          width: 96px;
          margin-right: 12px;
        }

        .crm-content {
          position: absolute;
        }
      }
      .edit-custom-form{
        width: 900px!important;
      }
      .left-form{
        width: 50%;
      }
      .right-form{
        width: 50%;
        padding-left: 20px;
        
        .agent-scope-vertical {
          //flex-direction: column !important;
          align-items: flex-start !important;
          margin-bottom: 16px;
          
          .el-form-item__label {
            width: auto !important;
            margin-bottom: 8px !important;
            padding-right: 0 !important;
            color: #E4E8F7;
            font-size: 14px;
          }
          
          .el-form-item__content {
            margin-left: 0 !important;
            width: 100% !important;
          }
        }
        
        .agent-scope-box {
          width: 100%;
          
          .agent-scope-info-box {
            display: flex;
            background: #000;
            width: 308px;
            min-height: 100px;
            padding: 16px 12px;
            flex-direction: column;
            justify-content: center;
            color: #C4C8CD;
            border-radius: 4px;
            border: 1px solid #3D4047;
            box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
            
            .scope-checkboxes-vertical {
              display: flex !important;
              flex-direction: column !important;
              //gap: 16px;
              
              .el-checkbox {
                margin-right: 0 !important;
                margin-bottom: 0 !important;
                display: block !important;
                
                .el-checkbox__label {
                  color: #C4C8CD !important;
                  font-size: 14px;
                  padding-left: 8px;
                  line-height: 16px;
                }
                
                .el-checkbox__input.is-disabled .el-checkbox__inner {
                  background-color: transparent !important;
                  border-color: #C4C8CD !important;
                  width: 16px !important;
                  height: 16px !important;
                  border-radius: 2px !important;
                }
                
                .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
                  background-color: #3760EA !important;
                  border-color: #3760EA !important;
                }
                
                .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
                  border-color: #fff !important;
                  width: 4px;
                  height: 8px;
                  left: 4px;
                  top: 1px;
                }
              }
            }
          }
        }
        
        .bank-info-box{
          display: flex;
          background: #000;
          width: 372px;
          min-height: 160px;
          padding: 16px 12px;
          flex-direction: column;
          justify-content: center;
          color: #C4C8CD;
          margin-top: 16px;
          border-radius: 4px;
          ul{
            line-height: 24px;
            .bank-title{
              flex: 1;
              float: right;
            }
            .bank-content{
              flex: 3;
            }
            .en-title{
              flex: 1.5;
            }
            .en-content{
              flex: 3;
            }
          }
        }
      }
      .edit-password-btn {
        margin-left: 12px;
      }
      .wait-examine, .unpass {
        font-weight: 700;
        font-size: 16px;
      }
      .wait-examine {
        color: $hg-warning-color;
      }
      .unpass {
        color: $hg-error-color;
      }
    }
    .content-right {
      flex: 1;
      height: 100%;
      border-radius: 4px;
      background: $hg-main-black;
      margin-right: 0;
      box-shadow: 0px 12px 32px 0px $hg-background-color,0px 8px 24px 0px $hg-background-color,0px 0px 16px 0px $hg-background-color;
    }
  }
}
</style>
<style>
.bank-info .el-input__inner{
  border: 1px solid #C74040!important;
}
.el-message-box{
  background: #141519;
  border-color: #3D4047;
  box-shadow: none;
}
.el-message-box__header .el-message-box__title{
  font-size: 14px;
}
.el-message-box__content{
  color: #E4E8F7;
}
.el-message-box__btns .el-button--small{
  background: #141519;
  color: #E4E8F7;
  border-color: #3D4047;
}
.el-message-box__btns .el-button--primary{
  background: #3760EA;
  color: #E4E8F7;
  border-color: #3760EA;
}
.edit-billFrom-label .el-form-item__label{
  width: 80px!important;
}
/* .edit-payInfo-label .el-form-item__label{
  width: 160px!important;
} */
.edit-business-label .el-form-item__label{
  width: 180px!important;
}

/* 代理范围checkbox样式 - 全局覆盖 */
.edit-custom-box .right-form .agent-scope-box .agent-scope-info-box .scope-checkboxes-vertical .el-checkbox.is-disabled .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #F3F5F766 !important;
  border-color: #F3F5F766 !important;
}

.edit-custom-box .right-form .agent-scope-box .agent-scope-info-box .scope-checkboxes-vertical .el-checkbox.is-disabled .el-checkbox__input.is-checked .el-checkbox__inner::after {
  border-color: #fff !important;
  width: 4px !important;
  height: 8px !important;
  left: 4px !important;
  top: 1px !important;
  border-width: 0 2px 2px 0 !important;
}

.edit-custom-box .right-form .agent-scope-box .agent-scope-info-box .scope-checkboxes-vertical .el-checkbox.is-disabled .el-checkbox__input .el-checkbox__inner {
  background-color: transparent !important;
  border-color: #C4C8CD !important;
  width: 16px !important;
  height: 16px !important;
  border-radius: 2px !important;
}

.edit-custom-box .right-form .agent-scope-box .agent-scope-info-box .scope-checkboxes-vertical .el-checkbox .el-checkbox__label {
  color: #C4C8CD !important;
  font-size: 14px !important;
  padding-left: 8px !important;
  line-height: 16px !important;
}
</style>
