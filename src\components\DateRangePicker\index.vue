<template>
  <div class="date-range-picker">
    <el-date-picker
      v-model="dateValue"
      :type="type"
      range-separator=""
      :format="format"
      :default-time="DEFAULT_TIME"
      :value-format="valueFormat"
      :start-placeholder="$t('component.date.start')"
      :end-placeholder="$t('component.date.end')"
      :clearable="clearable"
      :picker-options="pickerOptions"
      popper-class="date-range-picker"
      @change="handleChange">
      <hg-icon slot="range-separator" icon-name="icon-arrow-time-lab"></hg-icon>
    </el-date-picker>
    <hg-icon icon-name="icon-calendar-lab" class="date-suffix-icon" :color="variables.hgLabel"></hg-icon>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import moment from 'moment-timezone';
import variables from '@/assets/style/export.scss';

export default {
  name: 'DateRangePicker',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [String,Array],
      require: true
    },
    valueFormat: {
      type: String,
      default: 'yyyy-MM-dd HH:mm'
    },
    format: {
      type: String,
      default: 'yyyy-MM-dd HH:mm'
    },
    type: {
      type: String,
      default: 'datetimerange'
    },
    clearable: {
      type: Boolean,
      default: true
    },
    pickerOptions: {
      type: Object,
      default: () => {return {}}
    }
  },
  data(){
    return {
      variables,
      dateValue: [],
      DEFAULT_TIME: ['00:00:00', '23:59:59']
    }
  },
  computed: {
    ...mapGetters(['utcOffset'])
  },
  watch: {
    value: {
      immediate: true,
      handler(propValue) {
        if (propValue instanceof Array) {
          if (propValue[0] !== 0 && this.dateValue && this.dateValue.length === 0) {
            this.dateValue = this.formatTimeToBrower(propValue);
          }
        } 
      }
    }
  },
  methods: {
    handleChange(value){
      console.log('date-value: ', value);
      let newValue;
      if(!value){
        if(typeof this.value === 'string'){
          newValue = '';
        }else if(this.value instanceof Array){
          newValue = [0,0];
        }
      }else {
        
        if(value instanceof Array) {
          const startZoneTime = this.getTimeByUserTimezone(value[0]);
          const endZoneTime = this.getTimeByUserTimezone(value[1]);
          newValue = [startZoneTime, endZoneTime];
        }else {
          newValue = this.getTimeByUserTimezone(value);
        }
      }

      this.$emit('change',newValue);
    },

    /**
     * 页面看到的时间，理应是用户配置时区后的时间，但因为el-date还是根据浏览器的时区来显示的，因此需要特殊转换
     */
    getTimeByUserTimezone(inputTime) {
      
      let timestamp = inputTime;
      if(typeof inputTime === 'number') {
        if(inputTime === 0) {
          timestamp = Date.parse(new Date());
        }
      }else if(typeof inputTime === 'string') {
        if(inputTime){
          timestamp = Date.parse(new Date(inputTime));
        }else {
          timestamp = Date.parse(new Date());
        }
      }else {
        timestamp = Date.parse(new Date());
      }

      const time = new Date(timestamp); // 时间戳转成本地时间对象
      const year = time.getFullYear();
      let month = time.getMonth() + 1;
      let day = time.getDate();
      let hour = time.getHours();
      let minute = time.getMinutes();
      let second = time.getSeconds();
      month = String(month).padStart(2,0);
      day = String(day).padStart(2,0);
      hour = String(hour).padStart(2,0);
      minute = String(minute).padStart(2,0);
      second = String(second).padStart(2,0);

      const timeStr = `${year}-${month}-${day} ${hour}:${minute}:${second}`; // 这里是为了拿到本地时间
      
      moment.locale();
      const diffUTC = this.utcOffset - moment().utcOffset(); // 获取用户配置的时区 和浏览器时区之间的差值
      const timestampByUTC = moment(timeStr).subtract(diffUTC, 'm').format('YYYY-MM-DD HH:mm:ss'); // 根据获取的差值做减法
      const newTime = moment(timestampByUTC).format('x'); // 得到新的时间戳

      if(typeof inputTime === 'number') {
        return Number(newTime);
      }else if(typeof inputTime === 'string') {
        return String(newTime);
      }else {
        return newTime;
      }
    },

    /**
     * 反推
     */
    formatTimeToBrower(valueList) {
      moment.locale();
      const startTimeStr = moment(valueList[0]).utcOffset(this.utcOffset).format('YYYY-MM-DD HH:mm:ss');
      const endTimeStr = moment(valueList[1]).utcOffset(this.utcOffset).format('YYYY-MM-DD HH:mm:ss');
      const startTime = Date.parse(new Date(startTimeStr));
      const endTime = Date.parse(new Date(endTimeStr));
      return [startTime, endTime];
    },

  } 
}
</script>

<style lang="scss" scoped>
.date-range-picker {
  position: relative;

  .el-date-editor {
    width: 386px;
  }

  .date-suffix-icon {
    position: absolute;
    right: 24px;
    line-height: 40px;
  }
}
</style>

<style lang="scss">
@import './global.scss';
</style>
