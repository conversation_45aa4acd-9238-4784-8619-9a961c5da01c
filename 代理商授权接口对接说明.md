# 代理商授权接口对接说明

## 概述

本文档说明了在代理商账户管理页面中对接的两个新接口，用于获取代理商的授权账户信息和授权记录。

## 对接的接口

### 1. 获取组织某个类型的授权账户信息

**接口地址**: `/design-settlement-service/deviceAuth/getAuthAccountCountByType`
**请求方式**: `GET`
**功能**: 获取指定组织和授权类型的账户统计信息

**请求参数**:
- `authType` (string, 必填): 授权类型
- `orgCode` (integer, 必填): 组织编号

**响应数据**:
```javascript
{
  "code": 0,
  "data": {
    "currentCount": 0,    // 剩余授权数量
    "orgCode": 0,         // 组织编号
    "total": 0            // 总购买数
  },
  "detailMessage": "",
  "message": ""
}
```

### 2. 获取用户某个产品的授权记录列表（最近n条）

**接口地址**: `/design-settlement-service/deviceAuth/authRecordListNLimit`
**请求方式**: `GET`
**功能**: 获取指定组织和授权类型的最近n条授权记录

**请求参数**:
- `authType` (string, 必填): 授权类型
- `count` (integer, 必填): 获取记录数量
- `orgCode` (integer, 必填): 组织编号

**响应数据**:
```javascript
{
  "code": 0,
  "data": [
    {
      "agentOrg": "",           // 代理商客户名称
      "count": 0,               // 数量
      "date": "",               // 时间
      "deviceSn": "",           // 设备SN
      "operationType": 0,       // 操作类型 0：授权 1：取消授权
      "operator": "",           // 操作人
      "type": ""                // 产品名称
    }
  ],
  "detailMessage": "",
  "message": ""
}
```

### 3. 获取用户某个产品的授权记录列表（分页接口）

**接口地址**: `/design-settlement-service/deviceAuth/authRecordListPage`
**请求方式**: `POST`
**功能**: 获取指定组织的授权记录分页列表，支持筛选条件

**请求参数**:
```javascript
{
  "deviceType": "",    // 机型 (可选)
  "endTime": 0,        // 结束时间戳 (可选)
  "eventType": "",     // 事件类型 0：授权 1：取消授权 (可选)
  "pageNum": 0,        // 页码 (必填)
  "pageSize": 0,       // 每页记录数 (必填)
  "startTime": 0,      // 开始时间戳 (可选)
  "orgCode": 0         // 组织编号 (必填)
}
```

**响应数据**:
```javascript
{
  "code": 0,
  "data": {
    "current": 0,      // 当前页
    "pages": 0,        // 总页数
    "records": [       // 记录列表
      {
        "agentOrg": "",           // 代理商客户名称
        "count": 0,               // 数量
        "date": "",               // 时间
        "deviceSn": "",           // 设备SN
        "operationType": 0,       // 操作类型 0：授权 1：取消授权
        "operator": "",           // 操作人
        "type": ""                // 产品名称
      }
    ],
    "searchCount": true,
    "size": 0,         // 每页大小
    "total": 0         // 总记录数
  },
  "detailMessage": "",
  "message": ""
}
```

## 代码实现

### API 函数定义

在 `src/api/agent/index.js` 中添加了以下三个函数：

```javascript
// 获取组织某个类型的授权账户信息
export const getAuthAccountCountByType = (params) => {
  return http({
    url: '/design-settlement-service/deviceAuth/getAuthAccountCountByType',
    method: 'GET',
    params: params
  })
}

// 用户某个产品的授权记录列表,最近n条
export const getauthRecordListNLimit = (params) => {
  return http({
    url: '/design-settlement-service/deviceAuth/authRecordListNLimit',
    method: 'GET',
    params: params
  })
}

// 用户某个产品的授权记录列表,分页接口
export const getAuthRecordListPage = (data) => {
  return http({
    url: '/design-settlement-service/deviceAuth/authRecordListPage',
    method: 'POST',
    data: data
  })
}
```

### 组件集成

在 `src/views/AgentManagement/AgentAccount.vue` 中：

1. **导入API函数**:
```javascript
import { getAuthAccountCountByType, getauthRecordListNLimit } from '@/api/agent'
```

2. **添加数据加载逻辑**:
- `loadAuthData()`: 主要的数据加载方法
- `getAuthAccountInfo()`: 获取授权账户信息
- `getAuthRecords()`: 获取授权记录
- `formatAuthRecords()`: 格式化授权记录数据

3. **数据流程**:
- 当弹窗显示时，自动加载当前选项卡的数据
- 切换选项卡时，重新加载对应的数据
- 显示加载状态，处理错误情况

### 授权记录抽屉组件集成

在 `src/views/AgentManagement/AgentAuthorizationRecord.vue` 中：

1. **导入分页API函数**:
```javascript
import { getAuthRecordListPage } from '@/api/agent';
```

2. **替换模拟数据逻辑**:
- `loadAuthRecords()`: 使用真实的分页接口获取数据
- `formatAuthRecords()`: 格式化接口返回的数据
- `getDeviceTypeForAPI()`: 转换设备类型参数
- `getEventTypeText()`: 转换事件类型显示文本

3. **分页和筛选功能**:
- 支持按时间范围筛选（开始时间、结束时间）
- 支持按设备型号筛选
- 支持按事件类型筛选（授权/撤销授权）
- 完整的分页功能（页码、每页数量）

4. **数据流程**:
- 抽屉打开时自动加载第一页数据
- 切换设备类型时重新加载数据
- 修改筛选条件时重置到第一页并重新加载
- 分页操作时保持筛选条件不变

## 数据映射

### 授权类型映射
```javascript
const authTypeMap = {
  'a2d_hd_4k': 'a2d_hd_4k',
  'design_service': 'design_service', 
  'ai_software': 'ai_software'
}
```

### 事件类型映射
```javascript
const eventTypeMap = {
  0: '授权',
  1: '撤销授权'
}
```

### 数据字段映射

**接口返回数据格式化**:
- `date` → `operationTime` (操作时间)
- `operationType` → `eventType` (事件类型，经过文本转换)
- `agentOrg` → `customerName` (客户名称)
- `deviceSn` → `deviceSN` (设备SN)
- `operator` → `operator` (操作人)
- `count` → `addedCount` (数量)
- `type` → `deviceModel` (设备型号)

**分页接口参数映射**:
- 前端 `pageNo` → 接口 `pageNum`
- 前端 `pageSize` → 接口 `pageSize`
- 前端 `filterParams.startDate` → 接口 `startTime` (转换为时间戳)
- 前端 `filterParams.endDate` → 接口 `endTime` (转换为时间戳)
- 前端 `filterParams.eventType` → 接口 `eventType` (0或1)
- 前端 `filterParams.deviceModel` → 接口 `deviceType`

**分页响应数据映射**:
- 接口 `data.records` → 前端 `tableData.data`
- 接口 `data.total` → 前端 `totalUsers`
- 接口 `data.pages` → 前端 `totalPages`

## 国际化支持

添加了以下翻译键到 `src/i18n/config/zh.js`：
- `agent.authorize`: '授权'
- `agent.cancelAuthorize`: '撤销授权'
- `agent.loadDataError`: '加载数据失败'

## 错误处理

- 网络请求失败时显示错误提示
- 缺少必要参数时显示警告
- 加载状态的正确显示和隐藏
- 数据为空时的优雅处理

## 使用说明

1. 在代理商管理页面点击"管理账户"按钮
2. 系统会自动加载该代理商的授权信息
3. 可以切换不同的产品选项卡查看对应数据
4. 点击"查看更多"可以查看完整的授权记录

## 注意事项

1. 确保代理商数据中包含有效的 `orgCode`
2. 接口返回的时间格式需要与前端显示格式匹配
3. 授权类型参数需要与后端约定的值保持一致
4. 错误处理需要考虑各种异常情况

## 完整数据绑定流程

### 数据流向图
```
用户操作 → loadAuthData() → 并行调用两个接口 → 数据格式化 → 更新 tabsData → 响应式更新UI

具体流程：
1. 用户打开弹窗/切换选项卡
2. 调用 loadAuthData()
3. 并行执行：
   - getAuthAccountInfo() → 左侧统计卡片数据
   - getAuthRecords() → 右侧表格数据
4. 数据格式化和验证
5. 更新 this.tabsData[activeTab]
6. Vue响应式系统自动更新UI
```

### 关键计算属性
- `currentTabData()`: 获取当前选项卡的数据
- `displayRecords()`: 获取要显示的记录（最多3条）
- `showViewMore()`: 判断是否显示"查看更多"按钮

### 数据状态管理
- **加载中**: `loading: true` → 显示加载动画
- **有数据**: 正常显示统计信息和记录列表
- **无数据**: 显示"暂无授权记录"提示
- **错误**: 重置为默认值并显示错误提示

### UI响应式更新
```javascript
// 左侧统计卡片
{{ currentTabData.remainingAuth }}  // 剩余授权数
{{ currentTabData.totalPurchased }} // 总购买数

// 右侧表格
:data="displayRecords"              // 表格数据
v-if="!loading && displayRecords.length === 0"  // 无数据提示
v-if="showViewMore"                 // 查看更多按钮

// 抽屉组件
:auth-records="currentTabData.allRecords || []"  // 完整记录
```

## 测试建议

1. **正常流程测试**
   - 测试数据正常加载和显示
   - 测试选项卡切换功能
   - 测试"查看更多"功能

2. **异常情况测试**
   - 测试网络请求失败
   - 测试接口返回错误
   - 测试数据为空的情况
   - 测试缺少orgCode的情况

3. **UI状态测试**
   - 测试加载状态显示
   - 测试无数据提示显示
   - 测试错误提示显示

4. **数据一致性测试**
   - 测试切换选项卡后数据正确性
   - 测试并发请求的数据一致性
   - 测试数据格式化的正确性

5. **分页功能测试**
   - 测试分页导航功能
   - 测试每页数量变更
   - 测试跳转到指定页面
   - 测试分页数据的正确性

6. **筛选功能测试**
   - 测试时间范围筛选
   - 测试设备型号筛选
   - 测试事件类型筛选
   - 测试多条件组合筛选
   - 测试筛选条件重置

7. **国际化测试**
   - 测试中英文切换
   - 测试所有文本的翻译显示

## 接口对接完成状态

### ✅ 已完成
1. **账户统计接口** (`getAuthAccountCountByType`)
   - 用于主页面左侧统计卡片
   - 显示剩余授权数和总购买数

2. **最近记录接口** (`getauthRecordListNLimit`)
   - 用于主页面右侧表格
   - 显示最近3条授权记录

3. **分页记录接口** (`getAuthRecordListPage`)
   - 用于授权记录抽屉
   - 支持完整的分页和筛选功能

### 🔧 技术特点
- **并行数据加载**: 统计信息和记录列表同时获取
- **响应式数据绑定**: Vue响应式系统自动更新UI
- **完善的错误处理**: 网络异常、数据异常的优雅处理
- **用户体验优化**: 加载状态、无数据提示、错误提示
- **国际化支持**: 所有用户可见文本支持多语言
