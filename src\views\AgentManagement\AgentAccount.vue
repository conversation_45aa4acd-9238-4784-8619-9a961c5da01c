<template>
  <div v-if="show" class="agent-account-box">
    <!-- 头部导航栏 -->
    <div class="header">
      <p class="back-btn border finger" @click="goBack">
        <i class="iconfont icon-arrow-back" />{{ $t('agent.back') }}
      </p>
      <p class="header-title">{{ $t('agent.accountTitle') }}<em>{{ agentInfo.agentName || $t('agent.unnamedAgent') }}</em></p>
    </div>

    <!-- 自定义选项卡导航 -->
    <div class="header-tab">
      <span :class="['tab-item', activeTab === 'a2d_hd_4k' ? 'active' : '']" @click="changeTab('a2d_hd_4k')">A2D HD 4K</span>
      <!-- 预留其他选项卡 -->
      <!-- <span :class="['tab-item', activeTab === 'design_service' ? 'active' : '']" @click="changeTab('design_service')">设计服务</span>
      <span :class="['tab-item', activeTab === 'ai_software' ? 'active' : '']" @click="changeTab('ai_software')">AI软件</span> -->
    </div>

    <!-- 选项卡内容 -->
    <div class="tabs-content">
      <!-- A2D HD 4K 选项卡内容 -->
      <div v-if="activeTab === 'a2d_hd_4k'" class="tab-content">
        <!-- 主体内容 -->
        <div class="content-box">
          <!-- 左侧统计卡片区域 -->
          <div class="content-left">
            <div class="stats-container">
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="iconfont icon-icon_devicePrint"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-label">{{ $t('agent.remainingAuth') }}</div>
                  <div class="stat-value">{{ currentTabData.remainingAuth }}</div>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="iconfont icon-payments"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-label">{{ $t('agent.totalPurchased') }}</div>
                  <div class="stat-value">{{ currentTabData.totalPurchased }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧表格区域 -->
          <div class="content-right">
            <div class="table-container">
              <!-- 使用 newTable 组件 -->
              <new-table
                class="recent-records-table"
                :data="displayRecords"
                :loading="loading"
                :header-data="headerData"
                :hasIndex="false"
                max-height="200px"
              />

              <!-- 无数据提示 -->
              <div v-if="!loading && displayRecords.length === 0" class="no-data-tip">
                <i class="el-icon-document"></i>
                <span>{{ $t('agent.noAuthRecords') || '暂无授权记录' }}</span>
              </div>

              <!-- 查看更多 -->
              <div v-if="showViewMore" class="table-more">
                <span class="show-more" @click="openDrawer">{{ $t('agent.viewMore') }} ></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预留其他选项卡内容 -->
      <!-- <div v-if="activeTab === 'design_service'" class="tab-content">
        <div class="coming-soon">
          <p>{{ $t('agent.designServiceComingSoon') }}</p>
        </div>
      </div>
      <div v-if="activeTab === 'ai_software'" class="tab-content">
        <div class="coming-soon">
          <p>{{ $t('agent.aiSoftwareComingSoon') }}</p>
        </div>
      </div> -->
    </div>

    <!-- 授权记录抽屉组件 -->
    <AgentAuthorizationRecord
      :show.sync="showAuthRecord"
      :agent-info="agentData"
      :device-type="activeTab"
      :auth-records="currentTabData.allRecords || []"
    />
  </div>
</template>

<script>
import AgentAuthorizationRecord from './AgentAuthorizationRecord.vue'
import newTable from '@/components/func-components/newTable.vue'
import { getAuthAccountCountByType, getauthRecordListNLimit } from '@/api/agent'

export default {
  name: 'AuthRecord',
  components: {
    AgentAuthorizationRecord,
    newTable
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    agentData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      agentInfo: {
        orgCode: '',
        agentName: '',
        agentCode: '',
        userCode: '',
        email: '',
        mobile: '',
        mobilePrefix: '',
        status: '',
        tzCode: ''
      },
      // 当前激活的选项卡
      activeTab: 'a2d_hd_4k',
      // 授权记录抽屉显示状态
      showAuthRecord: false,
      // 数据加载状态
      loading: false,
      // 各选项卡的数据
      tabsData: {
        a2d_hd_4k: {
          remainingAuth: 0,
          totalPurchased: 0,
          recentRecords: [],
          allRecords: []
        },
        design_service: {
          remainingAuth: 0,
          totalPurchased: 0,
          recentRecords: [],
          allRecords: []
        },
        ai_software: {
          remainingAuth: 0,
          totalPurchased: 0,
          recentRecords: [],
          allRecords: []
        }
      }
    }
  },
  computed: {
    // 当前选项卡的数据
    currentTabData() {
      return this.tabsData[this.activeTab] || {}
    },
    // 表头数据配置
    headerData() {
      return [
        {
          prop: "operationTime",
          minWidth: "25%",
          noTip: false,
          getLabel: () => this.$t('agent.operationTime'),
        },
        {
          prop: "eventType",
          minWidth: "20%",
          noTip: false,
          getLabel: () => this.$t('agent.eventType'),
        },
        {
          prop: "customerName",
          minWidth: "25%",
          noTip: false,
          getLabel: () => this.$t('agent.endCustomer'),
        },
        {
          prop: "deviceSN",
          minWidth: "30%",
          noTip: false,
          getLabel: () => this.$t('agent.deviceSN'),
        }
      ];
    },
    // 显示的记录数据（最多3条）
    displayRecords() {
      const records = this.currentTabData.recentRecords || [];
      return records.slice(0, 3);
    },
    // 是否显示查看更多按钮
    showViewMore() {
      const records = this.currentTabData.recentRecords || [];
      return records.length > 3;
    }
  },
  watch: {
    show(val) {
      if (val && this.agentData) {
        // 从 props 接收数据，参考 EditAgent 的方式
        this.agentInfo = {
          orgCode: this.agentData.orgCode || '',
          agentName: this.agentData.orgName || this.agentData.agentName || '',
          agentCode: this.agentData.orgSn || this.agentData.agentCode || '',
        }

        console.log('代理商账户管理弹窗接收到的数据:', this.agentInfo)

        // 数据验证
        if (!this.agentInfo.orgCode) {
          console.warn('缺少必要的代理商组织编码')
          if (this.$MessageAlert) {
            this.$MessageAlert({
              text: this.$t('agent.missingOrgCode'),
              type: 'warning'
            })
          }
        } else {
          // 加载授权账户信息和记录
          this.loadAuthData()
        }
      }
    }
  },
  methods: {
    // 关闭弹窗，参考 EditAgent 的方式
    goBack() {
      this.$emit('update:show', false);
    },
    // 切换选项卡
    changeTab(tabType) {
      this.activeTab = tabType;
      console.log('切换到选项卡:', tabType);
      // 切换选项卡时关闭授权记录抽屉
      this.showAuthRecord = false;
      // 加载新选项卡的数据
      if (this.agentInfo.orgCode) {
        this.loadAuthData();
      }
    },
    // 打开授权记录抽屉
    openDrawer() {
      this.showAuthRecord = true;
      console.log('打开授权记录抽屉');
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        '1': this.$t('agent.statusNormal'),
        '0': this.$t('agent.statusPending'),
        '-1': this.$t('agent.statusRejected'),
        '2': this.$t('agent.statusDisabled')
      }
      return statusMap[status] || this.$t('agent.statusUnknown')
    },
    // 加载授权数据
    async loadAuthData() {
      if (!this.agentInfo.orgCode) {
        console.warn('缺少组织编码，无法加载授权数据')
        return
      }

      this.loading = true
      console.log(`开始加载 ${this.activeTab} 选项卡的授权数据...`)

      try {
        // 获取当前选项卡对应的授权类型
        const authType = this.getAuthTypeByTab(this.activeTab)
        console.log('授权类型:', authType, '组织编码:', this.agentInfo.orgCode)

        // 并行获取授权账户信息和授权记录
        const [accountInfo, authRecords] = await Promise.all([
          this.getAuthAccountInfo(authType),
          this.getAuthRecords(authType, 3) // 获取最近3条记录用于表格显示
        ])

        // 更新左侧统计卡片数据
        if (accountInfo) {
          this.tabsData[this.activeTab].remainingAuth = accountInfo.currentCount || 0
          this.tabsData[this.activeTab].totalPurchased = accountInfo.total || 0
          console.log('账户信息更新成功:', {
            remainingAuth: accountInfo.currentCount,
            totalPurchased: accountInfo.total
          })
        } else {
          // 如果没有获取到账户信息，重置为0
          this.tabsData[this.activeTab].remainingAuth = 0
          this.tabsData[this.activeTab].totalPurchased = 0
          console.log('未获取到账户信息，使用默认值')
        }

        // 更新右侧表格数据
        if (authRecords && authRecords.length > 0) {
          const formattedRecords = this.formatAuthRecords(authRecords)
          this.tabsData[this.activeTab].recentRecords = formattedRecords
          this.tabsData[this.activeTab].allRecords = formattedRecords
          console.log('授权记录更新成功，记录数量:', formattedRecords.length)
        } else {
          // 如果没有授权记录，清空数组
          this.tabsData[this.activeTab].recentRecords = []
          this.tabsData[this.activeTab].allRecords = []
          console.log('未获取到授权记录，使用空数组')
        }

        console.log('授权数据加载完成')

      } catch (error) {
        console.error('加载授权数据失败:', error)

        // 重置数据为默认值
        this.tabsData[this.activeTab].remainingAuth = 0
        this.tabsData[this.activeTab].totalPurchased = 0
        this.tabsData[this.activeTab].recentRecords = []
        this.tabsData[this.activeTab].allRecords = []

        if (this.$MessageAlert) {
          this.$MessageAlert({
            text: this.$t('agent.loadDataError') || '加载数据失败',
            type: 'error'
          })
        }
      } finally {
        this.loading = false
      }
    },
    // 根据选项卡获取授权类型
    getAuthTypeByTab(tabType) {
      const authTypeMap = {
        'a2d_hd_4k': 'A2D HD 4K',
        'design_service': 'design_service',
        'ai_software': 'ai_software'
      }
      return authTypeMap[tabType] || 'a2d_hd_4k'
    },
    // 获取授权账户信息
    async getAuthAccountInfo(authType) {
      try {
        const response = await getAuthAccountCountByType({
          authType: authType,
          orgCode: this.agentInfo.orgCode
        })

        if (response && response.code === 0) {
          return response.data
        } else {
          console.warn('获取授权账户信息失败:', response?.message)
          return null
        }
      } catch (error) {
        console.error('获取授权账户信息异常:', error)
        return null
      }
    },
    // 获取授权记录
    async getAuthRecords(authType, count = 3) {
      try {
        const response = await getauthRecordListNLimit({
          authType: authType,
          count: count,
          orgCode: this.agentInfo.orgCode
        })

        if (response && response.code === 0) {
          return response.data || []
        } else {
          console.warn('获取授权记录失败:', response?.message)
          return []
        }
      } catch (error) {
        console.error('获取授权记录异常:', error)
        return []
      }
    },
    // 格式化授权记录数据
    formatAuthRecords(records) {
      return records.map(record => ({
        operationTime: record.date || '',
        eventType: this.getEventTypeText(record.operationType),
        customerName: record.agentOrg || '',
        deviceSN: record.deviceSn || '',
        operator: record.operator || ''
      }))
    },
    // 获取事件类型文本
    getEventTypeText(operationType) {
      const eventTypeMap = {
        0: this.$t('agent.authorize') || '授权',
        1: this.$t('agent.cancelAuthorize') || '撤销授权'
      }
      return eventTypeMap[operationType] || this.$t('agent.unknown') || '未知'
    }
  }
};
</script>

<style lang="scss" scoped>
.agent-account-box {
  z-index: 99;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0px;
  left: 0;
  background: $hg-background-color;
  padding: 14px 24px 70px 24px;
  overflow-x: auto;
  overflow-y: hidden;

  .header {
    margin-bottom: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .back-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      font-size: 12px;
      height: $hg-height-32;
      color: $hg-secondary-fontcolor;
      i {
        margin-right: 8px;
      }
    }

    .header-title {
      color: $hg-primary-fontcolor;
      font-size: 16px;
      font-weight: bold;
      flex: 1;
      margin-left: 24px;

      em {
        color: $hg-main-blue;
        font-style: normal;
        margin-left: 8px;
      }
    }

    .header-actions {
      .action-btn {
        background-color: $hg-main-blue;
        border: none;
        color: $hg-primary-fontcolor;
        font-size: 12px;
        height: 32px;
        padding: 0 16px;
      }
    }
  }

  .header-tab {
    position: relative;
    height: 40px;
    margin-bottom: 24px;

    .tab-item {
      position: relative;
      display: inline-block;
      min-width: 104px;
      padding: 0 16px;
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      color: $hg-secondary-fontcolor;
      cursor: pointer;
      text-align: center;
      border-radius: 4px;
      margin-right: 8px;
      transition: all 0.3s ease;

      &:hover {
        color: $hg-primary-fontcolor;
        background: $hg-hover-bg-color;
      }

      &.active {
        color: $hg-primary-fontcolor;
        background: $hg-main-blue;
      }
    }
  }

  .tabs-content {
    height: calc(100% - 64px);

    .tab-content {
      height: 100%;
    }

    .coming-soon {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: $hg-secondary-fontcolor;
      font-size: 16px;
    }
  }

  .content-box {
    display: flex;
    height: 100%;
    gap: 24px;

    .content-left, .content-right {
      height: 50%;
      border-radius: 4px;
      background: $hg-main-black;
      box-shadow: 0px 12px 32px 0px $hg-background-color,0px 8px 24px 0px $hg-background-color,0px 0px 16px 0px $hg-background-color;
      padding: 16px 24px;

      .title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        height: 40px;

        .title-text {
          color: $hg-primary-fontcolor;
          font-weight: bold;
          font-size: 16px;
        }
      }
    }

    .content-left {
      width: 412px;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding: 8px 16px;
      gap: 8px;

      .stats-container {
        height: 248px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        width: 100%;

        .stat-card {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: 8px 24px;
          gap: 16px;
          width: 380px;
          height: 112px;
          background: #27292E;
          border-radius: 8px;
          flex: none;

          .stat-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #2F3238;
            border-radius: 8px;
            flex: none;

            i {
              font-size: 24px;
              color: #F3F5F7;
            }
          }

          .stat-content {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 0px;
            gap: 4px;
            flex: 1;

            .stat-label {
              color: #9EA2A8;
              font-family: 'Source Han Sans CN';
              font-style: normal;
              font-weight: 700;
              font-size: 14px;
              line-height: 20px;
              margin-bottom: 0;
              margin-right: 8px;
            }

            .stat-value {
              color: #F3F5F7;
              font-family: 'Oxygen';
              font-style: normal;
              font-weight: 700;
              font-size: 32px;
              line-height: 40px;
              display: flex;
              align-items: center;
              flex: none;
            }
          }
        }
      }
    }

    .content-right {
      flex: 1;

      .table-container {
        width: 100%;
        min-height: 248px;
        background: $hg-main-black;
        border-radius: 8px;
        padding: 16px;
        position: relative;
        display: flex;
        flex-direction: column;

        .recent-records-table {
          flex: none;

          ::v-deep .hg-table {
            .el-table {
              background: transparent;

              .el-table__header-wrapper {
                .header-row-item {
                  th {
                    background: transparent;
                    color: #C4C8CD;
                    border-bottom: 1px solid #38393D;
                    font-size: 12px;
                    height: 40px;
                    padding: 8px 12px;
                  }
                }
              }

              .el-table__body-wrapper {
                .row-item {
                  background: transparent;

                  &:hover {
                    background-color: #27292E;

                    td {
                      background-color: #27292E;
                    }
                  }

                  .cell-column-item {
                    color: #F3F5F7;
                    font-size: 14px;
                    height: 48px;
                    border-bottom: 1px dashed #2d2f33;
                    padding: 12px;
                  }
                }
              }
            }
          }
        }

        .no-data-tip {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px 20px;
          color: $hg-secondary-fontcolor;
          font-size: 14px;

          i {
            font-size: 48px;
            margin-bottom: 12px;
            opacity: 0.5;
          }

          span {
            opacity: 0.8;
          }
        }

        .table-more {
          margin-top: 16px;
          text-align: center;
          padding: 8px 0;

          .show-more {
            color: $hg-main-blue;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            font-weight: 500;

            &:hover {
              color: $hg-button-hover-fontcolor;
            }
          }
        }
      }
    }
  }
}
</style>
