import http from '../request'

/** 获取代理商列表（分页）
 * @params {
 * "key": "",        // 代理编码、代理名称
 * "pageNo": 0,      // 当前页码
 * "pageSize": 0,    // 每页数量
 * } data
 */
export const getAgentList = (params) => {
  return http({
    url: '/user-basic/agencyLab/v1/agencyListByPage',
    method: 'POST',
    data: params,
    showTip: true
  })
}

/** 获取代理商信息
 * @params {
 * "orgCode": 0,  // 代理商组织编号
 * } data
 */
export const getAgentInfo = (params) => {
  return http({
    url: '/user-basic/agencyLab/v1/info',
    method: 'POST',
    data: params,
  })
}


/**新增代理商
 * @params {
 * "businessUserCodes": [],  // 关联业务人员用户编码 (可选)
 * "email": "",              // 邮箱 (必填)
 * "mobile": "",             // 手机号 (可选)
 * "mobilePrefix": "",       // 手机号前缀 (可选)
 * "orgLeader": "",          // 负责人 (可选)
 * "orgSn": "",              // 代理编码 (必填)
 * "rangeType": [],          // 代理范围 0：设备 1：设计服务 2：AI软件 (必填)
 * "supportUserCodes": [],   // 关联技术支持用户编码 (可选)
 * "tzCode": 0               // 时区编码 (必填)
 * } params
 */
export const addAgent = (params) => {
  return http({
    url: '/user-basic/agencyLab/v1/add',
    method: 'POST',
    data: params,
  })
}

/**编辑代理商
 * @params {
 * "accountCode": 0,          // 银行账户编号 (可选)
 * "businessUserCodes": [],   // 关联业务人员用户编码 (可选)
 * "headerCode": 0,           // 账单抬头编号 (可选)
 * "mobile": "",              // 手机号 (可选)
 * "mobilePrefix": "",        // 手机号前缀 (可选)
 * "orgAddress": "",          // 地址 (可选)
 * "orgCode": 0,              // 代理商组织编号 (必填)
 * "orgLeader": "",           // 负责人 (可选)
 * "rangeType": [],           // 代理范围 0：设备 1：设计服务 2：AI软件 (可选)
 * "settlementCurrency": 0,   // 结算币种 0:美元 USD 1:人民币 CNY 2：欧元 EUR 3:日元 JPY (必填)
 * "supportUserCodes": [],    // 关联技术支持用户编码 (可选)
 * "tzCode": 0                // 时区编码 (可选)
 * } params
 */
export const editAgent = (params) => {
  return http({
    url: '/user-basic/agencyLab/v1/edit',
    method: 'POST',
    data: params,
  })
}

/** 导出代理商交易明细
 * @params {
 * "startTime": 0,  // 开始时间戳
 * "endTime": 0,    // 结束时间戳
 * } params
 */
export const exportAgentTransaction = (params) => {
  return http({
    url: '/user-basic/agencyLab/v1/exportTransaction',
    method: 'POST',
    data: params,
  })
}

/**启用-禁用代理商
 * @params {
 * "orgCode": 0,  // 代理商组织编码 (必填)
 * "status": 0    // 1：启用 2：禁用 (必填)
 * } params
 */
export const changeAgentStatus = (params) => {
  return http({
    url: '/user-basic/agencyLab/v1/status',
    method: 'POST',
    data: params,
  })
}

// 获取代理商绑定的客户列表
export const getAgentCustomerList = (params) => {
  return http({
    url: '/user-basic/agencyLab/v1/agencyOrgList',
    method: 'POST',
    data: params,
    showTip: true
  })
}

/** 获取组织某个类型的授权账户信息
 * @params {
 * "authType": "",     // 授权类型 (必填)
 * "orgCode": 0        // 组织编号 (必填)
 * } params
 */
export const getAuthAccountCountByType = (params) => {
  return http({
    url: '/design-settlement-service/deviceAuth/getAuthAccountCountByType',
    method: 'GET',
    params: params
  })
}

/** 用户某个产品的授权记录列表,最近n条
 * @params {
 * "authType": "",     // 授权类型 (必填)
 * "count": 0,         // 数量 (必填)
 * "orgCode": 0        // 组织编号 (必填)
 * } params
 */
export const getauthRecordListNLimit = (params) => {
  return http({
    url: '/design-settlement-service/deviceAuth/authRecordListNLimit',
    method: 'GET',
    params: params
  })
}

/** 用户某个产品的授权记录列表,分页接口
 * @params {
 * "deviceType": "",   // 机型 (可选)
 * "endTime": 0,       // 结束时间 (可选)
 * "eventType": "",    // 事件类型 (可选)
 * "pageNum": 0,       // 页码 (必填)
 * "pageSize": 0,      // 每页记录数 (必填)
 * "startTime": 0,     // 开始时间 (可选)
 * "orgCode": 0        // 组织编号 (必填，通过URL参数传递)
 * } data
 */
export const getAuthRecordListPage = (data) => {
  return http({
    url: '/design-settlement-service/deviceAuth/authRecordListPage',
    method: 'POST',
    data: data
  })
}


